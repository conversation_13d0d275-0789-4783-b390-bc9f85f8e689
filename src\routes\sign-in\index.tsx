import { $, component$, useSignal } from '@qwik.dev/core';
import { Link, useNavigate } from '@qwik.dev/router';
import XCircleIcon from '~/components/icons/XCircleIcon';
import { loginMutation } from '~/providers/shop/account/account';

export default component$(() => {
	const navigate = useNavigate();
	const email = useSignal('');
	const password = useSignal('');
	const rememberMe = useSignal(true);
	const error = useSignal('');

	const login = $(async () => {
		const { login } = await loginMutation(email.value, password.value, rememberMe.value);
		if (login.__typename === 'CurrentUser') {
			navigate('/account');
		} else {
			error.value = login.message;
		}
	});
	return (
		<div class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-background">
			<div class="sm:mx-auto sm:w-full sm:max-w-md">
				<h2 class="mt-6 text-center text-3xl font-bold text-text">Sign in to your account</h2>
				<p class="mt-2 text-center text-sm text-gray-400">
					Or{' '}
					<Link
						href="/sign-up"
						class="font-medium text-primary hover:text-secondary transition-colors"
					>
						create a new account
					</Link>
				</p>
			</div>

			<div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
				<div class="bg-dark-surface py-8 px-4 shadow sm:rounded-lg sm:px-10">
					<div class="space-y-6">
						<div>
							<label class="block text-sm font-medium text-text">Email</label>
							<div class="mt-1">
								<input
									type="email"
									value={email.value}
									required
									onInput$={(_, el) => (email.value = el.value)}
									onKeyUp$={(ev, el) => {
										error.value = '';
										if (ev.key === 'Enter' && !!el.value && !!password.value) {
											login();
										}
									}}
									class="appearance-none block w-full px-3 py-2 border border-dark-border bg-dark-surface text-text rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
								/>
							</div>
						</div>

						<div>
							<label class="block text-sm font-medium text-text">Password</label>
							<div class="mt-1">
								<input
									type="password"
									value={password.value}
									required
									onInput$={(_, el) => (password.value = el.value)}
									onKeyUp$={(ev, el) => {
										error.value = '';
										if (ev.key === 'Enter' && !!el.value && !!email.value) {
											login();
										}
									}}
									class="appearance-none block w-full px-3 py-2 border border-dark-border bg-dark-surface text-text rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
								/>
							</div>
						</div>

						<div class="flex items-center justify-between">
							<div class="flex items-center">
								<input
									id="remember-me"
									name="remember-me"
									type="checkbox"
									checked={rememberMe.value}
									onChange$={(_, el) => (rememberMe.value = el.checked)}
									class="h-4 w-4 text-primary focus:ring-primary border-dark-border bg-dark-surface rounded"
								/>
								<label html-for="remember-me" class="ml-2 block text-sm text-text">
									Remember me
								</label>
							</div>

							<div class="text-sm">
								<Link
									href="/forgot-password"
									class="font-medium text-primary hover:text-secondary transition-colors"
								>
									Forgot your password?
								</Link>
							</div>
						</div>

						{error.value !== '' && (
							<div class="rounded-md bg-red-900/20 border border-red-800 p-4">
								<div class="flex">
									<div class="flex-shrink-0">
										<XCircleIcon />
									</div>
									<div class="ml-3">
										<h3 class="text-sm font-medium text-red-300">
											We ran into a problem signing you in!
										</h3>
										<p class="text-sm text-red-400 mt-2">{error.value}</p>
									</div>
								</div>
							</div>
						)}
						<div>
							<button
								class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200"
								onClick$={login}
							>
								Sign in
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
});
