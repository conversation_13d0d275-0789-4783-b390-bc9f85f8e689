import { component$ } from '@qwik.dev/core';
import { Link } from '@qwik.dev/router';
import { Image } from 'qwik-image';
import Price from './Price';

export default component$(
	({ productAsset, productName, slug, priceWithTax, currencyCode }: any) => {
		return (
			<Link class="flex flex-col mx-auto group" href={`/products/${slug}/`}>
				<Image
					layout="fixed"
					class="rounded-xl flex-grow object-cover aspect-[7/8] transition-transform group-hover:scale-105"
					width="200"
					height="200"
					src={productAsset?.preview + '?w=300&h=400&format=webp'}
					alt={`Image of: ${productName}`}
				/>
				<div class="h-2" />
				<div class="text-sm text-white font-medium">{productName}</div>
				<Price
					priceWithTax={priceWithTax}
					currencyCode={currencyCode}
					forcedClass="text-sm font-medium text-accent1"
				/>
			</Link>
		);
	}
);
