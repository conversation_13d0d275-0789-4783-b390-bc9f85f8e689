import { $, component$, useSignal } from '@qwik.dev/core';
import { Link } from '@qwik.dev/router';
import { $localize } from '~/utils/localize';

export default component$(() => {
	const formSubmitted = useSignal(false);
	const formError = useSignal(false);
	const formName = useSignal('');
	const formEmail = useSignal('');
	const formSubject = useSignal('');
	const formMessage = useSignal('');

	// This would be replaced with actual form submission logic
	const handleSubmit = $(() => {
		if (formName.value && formEmail.value && formMessage.value) {
			// In a real implementation, this would send the form data to a server
			console.log('Form submitted:', {
				name: formName.value,
				email: formEmail.value,
				subject: formSubject.value,
				message: formMessage.value,
			});

			formSubmitted.value = true;
			formError.value = false;

			// Reset form
			formName.value = '';
			formEmail.value = '';
			formSubject.value = '';
			formMessage.value = '';
		} else {
			formError.value = true;
		}
	});

	return (
		<div class="bg-background">
			{/* Hero Section */}
			<div class="relative py-16 bg-gradient-to-b from-black to-background">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center">
						<h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">
							{$localize`Contact Us`}
						</h1>
						<p class="mt-4 max-w-3xl mx-auto text-xl text-text">
							{$localize`We would love to hear from you. Get in touch with our team.`}
						</p>
					</div>
				</div>
			</div>

			{/* Contact Section */}
			<div class="py-16">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
						{/* Contact Form */}
						<div class="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg shadow-lg p-8">
							<h2 class="text-2xl font-bold text-accent1 mb-6">{$localize`Send us a message`}</h2>

							{formSubmitted.value && (
								<div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
									{$localize`Thank you for your message! We will get back to you soon.`}
								</div>
							)}

							{formError.value && (
								<div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
									{$localize`Please fill out all required fields.`}
								</div>
							)}

							<form class="space-y-6">
								<div>
									<label for="name" class="block text-sm font-medium text-text mb-1">
										{$localize`Name`} <span class="text-red-500">*</span>
									</label>
									<input
										id="name"
										name="name"
										type="text"
										required
										class="input-text w-full"
										value={formName.value}
										onInput$={(e) => (formName.value = (e.target as HTMLInputElement).value)}
									/>
								</div>

								<div>
									<label for="email" class="block text-sm font-medium text-text mb-1">
										{$localize`Email`} <span class="text-red-500">*</span>
									</label>
									<input
										id="email"
										name="email"
										type="email"
										required
										class="input-text w-full"
										value={formEmail.value}
										onInput$={(e) => (formEmail.value = (e.target as HTMLInputElement).value)}
									/>
								</div>

								<div>
									<label for="subject" class="block text-sm font-medium text-text mb-1">
										{$localize`Subject`}
									</label>
									<input
										id="subject"
										name="subject"
										type="text"
										class="input-text w-full"
										value={formSubject.value}
										onInput$={(e) => (formSubject.value = (e.target as HTMLInputElement).value)}
									/>
								</div>

								<div>
									<label for="message" class="block text-sm font-medium text-text mb-1">
										{$localize`Message`} <span class="text-red-500">*</span>
									</label>
									<textarea
										id="message"
										name="message"
										rows={6}
										required
										class="input-text w-full"
										value={formMessage.value}
										onInput$={(e) => (formMessage.value = (e.target as HTMLTextAreaElement).value)}
									></textarea>
								</div>

								<div>
									<button type="button" onClick$={handleSubmit} class="btn-primary w-full">
										{$localize`Send Message`}
									</button>
								</div>
							</form>
						</div>

						{/* Contact Information */}
						<div>
							<h2 class="text-2xl font-bold text-accent1 mb-6">{$localize`Get in touch`}</h2>

							<div class="space-y-8">
								<div class="flex items-start">
									<div class="flex-shrink-0">
										<span class="inline-flex items-center justify-center h-12 w-12 rounded-md bg-accent1 shadow-lg">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												class="h-6 w-6 text-white"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
												/>
											</svg>
										</span>
									</div>
									<div class="ml-4">
										<h3 class="text-lg font-medium text-accent1">{$localize`Phone`}</h3>
										<p class="mt-1 text-text">
											<Link href="tel:******-123-4567" class="hover:text-accent1 transition-colors">
												+****************
											</Link>
										</p>
										<p class="mt-1 text-sm text-primary">{$localize`Monday-Friday: 9am-5pm EST`}</p>
									</div>
								</div>

								<div class="flex items-start">
									<div class="flex-shrink-0">
										<span class="inline-flex items-center justify-center h-12 w-12 rounded-md bg-accent1 shadow-lg">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												class="h-6 w-6 text-white"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
												/>
											</svg>
										</span>
									</div>
									<div class="ml-4">
										<h3 class="text-lg font-medium text-accent1">{$localize`Email`}</h3>
										<p class="mt-1 text-text">
											<a
												href="mailto:<EMAIL>"
												class="hover:text-accent1 transition-colors"
											>
												<EMAIL>
											</a>
										</p>
										<p class="mt-1 text-sm text-primary">
											{$localize`We will respond as soon as possible`}
										</p>
									</div>
								</div>

								<div class="flex items-start">
									<div class="flex-shrink-0">
										<span class="inline-flex items-center justify-center h-12 w-12 rounded-md bg-accent1 shadow-lg">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												class="h-6 w-6 text-white"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
												/>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
												/>
											</svg>
										</span>
									</div>
									<div class="ml-4">
										<h3 class="text-lg font-medium text-accent1">{$localize`Location`}</h3>
										<p class="mt-1 text-text">
											123 Creative Avenue
											<br />
											Suite 456
											<br />
											Brooklyn, NY 11201
										</p>
									</div>
								</div>
							</div>

							{/* Social Media */}
							<div class="mt-12">
								<h3 class="text-lg font-medium text-accent1 mb-4">{$localize`Connect with us`}</h3>
								<div class="flex space-x-6">
									<Link href="#" class="text-text hover:text-accent1 transition-colors">
										<span class="sr-only">Facebook</span>
										<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
											<path
												fill-rule="evenodd"
												d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
												clip-rule="evenodd"
											/>
										</svg>
									</Link>
									<Link href="#" class="text-text hover:text-accent1 transition-colors">
										<span class="sr-only">Instagram</span>
										<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
											<path
												fill-rule="evenodd"
												d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
												clip-rule="evenodd"
											/>
										</svg>
									</Link>
									<Link href="#" class="text-text hover:text-accent1 transition-colors">
										<span class="sr-only">Twitter</span>
										<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
											<path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
										</svg>
									</Link>
									<Link href="#" class="text-text hover:text-accent1 transition-colors">
										<span class="sr-only">LinkedIn</span>
										<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
											<path
												fill-rule="evenodd"
												d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"
												clip-rule="evenodd"
											/>
										</svg>
									</Link>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Map Section */}
			<div class="py-16 bg-gradient-to-r from-primary/10 to-secondary/10">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center mb-12">
						<h2 class="text-3xl font-extrabold text-accent1 sm:text-4xl">
							{$localize`Visit Our Studio`}
						</h2>
						<p class="mt-4 max-w-3xl mx-auto text-lg text-text">
							{$localize`Come see our production facility and showroom in person.`}
						</p>
					</div>

					<div class="bg-background rounded-lg shadow-lg overflow-hidden">
						<div class="h-96 bg-gray-300 flex items-center justify-center">
							<p class="text-gray-600 text-center p-8">
								{$localize`Interactive map would be displayed here.`}
								<br />
								{$localize`For implementation, you could embed Google Maps or another mapping service.`}
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* FAQ Section */}
			<div class="py-16">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center mb-12">
						<h2 class="text-3xl font-extrabold text-accent1 sm:text-4xl">
							{$localize`Frequently Asked Questions`}
						</h2>
						<p class="mt-4 max-w-3xl mx-auto text-lg text-text">
							{$localize`Find quick answers to common questions.`}
						</p>
					</div>

					<div class="mt-12 space-y-6">
						<div class="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg shadow-md p-6">
							<h3 class="text-lg font-medium text-accent1">
								{$localize`What is the minimum order quantity?`}
							</h3>
							<p class="mt-2 text-text">
								{$localize`Our minimum order quantity varies by product and customization method. For screen printing, we typically require a minimum of 24 pieces, while for DTG printing and embroidery, we can accommodate orders as small as 12 pieces. For specific requirements, please contact us directly.`}
							</p>
						</div>

						<div class="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg shadow-md p-6">
							<h3 class="text-lg font-medium text-accent1">
								{$localize`What is your typical turnaround time?`}
							</h3>
							<p class="mt-2 text-text">
								{$localize`Standard production time is 10-14 business days after design approval. Rush orders may be available for an additional fee, with turnaround times as quick as 5-7 business days. Large orders may require additional time.`}
							</p>
						</div>

						<div class="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg shadow-md p-6">
							<h3 class="text-lg font-medium text-accent1">
								{$localize`Do you offer design services?`}
							</h3>
							<p class="mt-2 text-text">
								{$localize`Yes, we have an in-house design team that can help bring your ideas to life. We offer design services ranging from simple text layouts to complex custom illustrations. Design fees vary based on complexity and are often waived for larger orders.`}
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
});
