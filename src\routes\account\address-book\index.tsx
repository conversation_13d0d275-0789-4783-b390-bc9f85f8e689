import { component$, useContext, useSignal, useVisibleTask$ } from '@qwik.dev/core';
import { useNavigate } from '@qwik.dev/router';
import AddressCard from '~/components/account/AddressCard';
import { HighlightedButton } from '~/components/buttons/HighlightedButton';
import PlusIcon from '~/components/icons/PlusIcon';
import { APP_STATE } from '~/constants';
import { Address } from '~/generated/graphql';
import {
	deleteCustomerAddressMutation,
	getActiveCustomerAddressesQuery,
} from '~/providers/shop/customer/customer';
import { ShippingAddress } from '~/types';

export default component$(() => {
	const navigate = useNavigate();
	const appState = useContext(APP_STATE);
	const activeCustomerAddresses = useSignal<{ id: string; addresses: ShippingAddress[] }>();

	useVisibleTask$(async () => {
		const activeCustomer = await getActiveCustomerAddressesQuery();
		const { id, addresses } = activeCustomer;
		const shippingAddresses: ShippingAddress[] = (addresses as Address[]).map(
			(address: Address) =>
				({
					id: address.id,
					fullName: address.fullName,
					streetLine1: address.streetLine1,
					streetLine2: address.streetLine2,
					company: address.company,
					city: address.city,
					province: address.province,
					postalCode: address.postalCode,
					countryCode: address.country.code, // Updated to countryCode
					phoneNumber: address.phoneNumber,
					defaultShippingAddress: address.defaultShippingAddress,
					defaultBillingAddress: address.defaultBillingAddress,
				}) as ShippingAddress
		);
		activeCustomerAddresses.value = { id, addresses: shippingAddresses };

		if (activeCustomer?.addresses) {
			appState.addressBook.splice(0, appState.addressBook.length);
			appState.addressBook.push(...shippingAddresses);
		}
	});

	return activeCustomerAddresses.value ? (
		<div class="max-w-6xl mx-auto p-4">
			{appState.addressBook.length > 0 ? (
				<>
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
						{[...appState.addressBook].map((address) => (
							<AddressCard
								key={address.id}
								address={address}
								onDelete$={async (id) => {
									try {
										await deleteCustomerAddressMutation(id);
										// Optimistically update state without full page reload
										appState.addressBook = appState.addressBook.filter((a) => a.id !== id);
									} catch (error) {
										console.error('Failed to delete address:', error);
									}
								}}
							/>
						))}
					</div>
					<div class="flex justify-center">
						<HighlightedButton
							onClick$={() => {
								navigate('/account/address-book/add');
							}}
						>
							<PlusIcon /> &nbsp; Add New Address
						</HighlightedButton>
					</div>
				</>
			) : (
				<div class="bg-gray-800 border border-gray-600 rounded-lg p-8 text-center">
					<p class="text-gray-400 text-lg mb-4">No addresses found</p>
					<p class="text-gray-500 text-sm mb-6">Add your first address to make checkout faster.</p>
					<HighlightedButton
						onClick$={() => {
							navigate('/account/address-book/add');
						}}
					>
						<PlusIcon /> &nbsp; Add Address
					</HighlightedButton>
				</div>
			)}
		</div>
	) : (
		<div class="max-w-6xl mx-auto p-4">
			<div class="bg-gray-800 border border-gray-600 rounded-lg p-8 text-center">
				<p class="text-gray-400">Loading addresses...</p>
			</div>
		</div>
	);
});
