import { Component, component$ } from '@qwik.dev/core';
import { Link } from '@qwik.dev/router';

interface IProps {
	Icon: Component<{ class: string }>;
	text: string;
	href: string;
	isActive: boolean;
}

export const Tab = component$(({ Icon, text, href, isActive }: IProps) => {
	return (
		<li>
			<Link
				href={href}
				class={`group w-full gap-x-2 max-w-[12rem] inline-flex items-center justify-around p-4 rounded-t-lg border-b-2 transition-colors ${
					isActive
						? 'text-primary border-primary'
						: 'text-gray-400 border-transparent hover:text-gray-300 hover:border-gray-500'
				}`}
			>
				<Icon
					class={`w-5 h-5 ${isActive ? 'text-primary' : 'text-gray-400 group-hover:text-gray-300'}`}
				/>
				<p class="flex-1">{text}</p>
			</Link>
		</li>
	);
});
