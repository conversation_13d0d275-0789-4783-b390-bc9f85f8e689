import { $, component$, useSignal } from '@qwik.dev/core';
import { Link } from '@qwik.dev/router';
import XCircleIcon from '~/components/icons/XCircleIcon';
import { registerCustomerAccountMutation } from '~/providers/shop/account/account';
import { isEnvVariableEnabled } from '~/utils';
export default component$(() => {
	const email = useSignal('');
	const firstName = useSignal('');
	const lastName = useSignal('');
	const password = useSignal('');
	const confirmPassword = useSignal('');
	const successSignal = useSignal(false);
	const error = useSignal('');
	const registerCustomer = $(async (): Promise<void> => {
		if (
			email.value === '' ||
			firstName.value === '' ||
			lastName.value === '' ||
			password.value === ''
		) {
			error.value = 'All fields are required';
		} else if (password.value !== confirmPassword.value) {
			error.value = 'Passwords do not match';
		} else {
			error.value = '';
			successSignal.value = false;

			const { registerCustomerAccount } = await registerCustomerAccountMutation({
				input: {
					emailAddress: email.value,
					firstName: firstName.value,
					lastName: lastName.value,
					password: password.value,
				},
			});
			if (registerCustomerAccount.__typename === 'Success') {
				successSignal.value = true;
			} else {
				error.value = registerCustomerAccount.message;
			}
		}
	});

	return (
		<div class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-background">
			<div class="sm:mx-auto sm:w-full sm:max-w-md">
				<h2 class="mt-6 text-center text-3xl font-bold text-text">Create a new account</h2>
				<p class="mt-2 text-center text-sm text-gray-400">
					Or{' '}
					<Link
						href="/sign-in"
						class="font-medium text-primary hover:text-secondary transition-colors"
					>
						login to your existing account
					</Link>
				</p>
			</div>

			<div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
				<div class="bg-dark-surface py-8 px-4 shadow sm:rounded-lg sm:px-10">
					{successSignal.value && (
						<div class="mb-6 bg-green-900/20 border border-green-800 text-green-300 rounded p-4 text-center text-sm">
							<p>
								Account registration successful! We sent email verification to {email.value}, you
								must verify before logging in.
							</p>
						</div>
					)}
					{isEnvVariableEnabled('VITE_IS_READONLY_INSTANCE') && (
						<div class="mb-6 bg-blue-900/20 border border-blue-800 text-blue-300 rounded p-4 text-center text-sm">
							<p>
								Account registration is not available in demo mode. Please connect to your own
								Vendure instance to enable registration.
							</p>
						</div>
					)}
					<div class="space-y-6">
						<div>
							<label class="block text-sm font-medium text-text">Email address</label>
							<div class="mt-1">
								<input
									type="email"
									value={email.value}
									required
									onInput$={(_, el) => (email.value = el.value)}
									class="appearance-none block w-full px-3 py-2 border border-dark-border bg-dark-surface text-text rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
								/>
							</div>
						</div>

						<div>
							<label class="block text-sm font-medium text-text">First name</label>
							<div class="mt-1">
								<input
									type="text"
									value={firstName.value}
									required
									onInput$={(_, el) => (firstName.value = el.value)}
									class="appearance-none block w-full px-3 py-2 border border-dark-border bg-dark-surface text-text rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
								/>
							</div>
						</div>

						<div>
							<label class="block text-sm font-medium text-text">Last name</label>
							<div class="mt-1">
								<input
									type="text"
									value={lastName.value}
									required
									onInput$={(_, el) => (lastName.value = el.value)}
									class="appearance-none block w-full px-3 py-2 border border-dark-border bg-dark-surface text-text rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
								/>
							</div>
						</div>

						<div>
							<label class="block text-sm font-medium text-text">Password</label>
							<div class="mt-1">
								<input
									type="password"
									value={password.value}
									required
									onInput$={(_, el) => (password.value = el.value)}
									class="appearance-none block w-full px-3 py-2 border border-dark-border bg-dark-surface text-text rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
								/>
							</div>
						</div>

						<div>
							<label class="block text-sm font-medium text-text">Repeat Password</label>
							<div class="mt-1">
								<input
									type="password"
									value={confirmPassword.value}
									required
									onInput$={(_, el) => (confirmPassword.value = el.value)}
									class="appearance-none block w-full px-3 py-2 border border-dark-border bg-dark-surface text-text rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
								/>
							</div>
						</div>

						{error.value !== '' && (
							<div class="rounded-md bg-red-900/20 border border-red-800 p-4">
								<div class="flex">
									<div class="flex-shrink-0">
										<XCircleIcon />
									</div>
									<div class="ml-3">
										<h3 class="text-sm font-medium text-red-300">
											We ran into a problem creating your account!
										</h3>
										<p class="text-sm text-red-400 mt-2">{error.value}</p>
									</div>
								</div>
							</div>
						)}
						<div>
							<button
								class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200"
								onClick$={registerCustomer}
							>
								Create account
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
});
