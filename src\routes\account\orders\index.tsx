import { component$, useSignal, useVisibleTask$ } from '@qwik.dev/core';
import OrderCard from '~/components/account/OrderCard';
import { Customer, Order } from '~/generated/graphql';
import { getActiveCustomerOrdersQuery } from '~/providers/shop/customer/customer';

export default component$(() => {
	const activeCustomerOrdersSignal = useSignal<Customer>();

	useVisibleTask$(async () => {
		activeCustomerOrdersSignal.value = await getActiveCustomerOrdersQuery();
	});

	return activeCustomerOrdersSignal.value ? (
		<div class="max-w-6xl mx-auto p-4">
			{(activeCustomerOrdersSignal.value?.orders?.items || []).length > 0 ? (
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{(activeCustomerOrdersSignal.value?.orders?.items || []).map((order: Order) => (
						<OrderCard key={order.id} order={order} />
					))}
				</div>
			) : (
				<div class="bg-gray-800 border border-gray-600 rounded-lg p-8 text-center">
					<p class="text-gray-400 text-lg">No orders found</p>
					<p class="text-gray-500 text-sm mt-2">
						Your order history will appear here once you make your first purchase.
					</p>
				</div>
			)}
		</div>
	) : (
		<div class="max-w-6xl mx-auto p-4">
			<div class="bg-gray-800 border border-gray-600 rounded-lg p-8 text-center">
				<p class="text-gray-400">Loading orders...</p>
			</div>
		</div>
	);
});
